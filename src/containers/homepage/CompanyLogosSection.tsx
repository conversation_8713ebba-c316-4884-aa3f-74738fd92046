// material-ui
import { Box, Container, keyframes } from '@mui/material';

const companyLogos = [
    {
        id: 1,
        name: 'Google',
        logo: 'https://img.logo.dev/google.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 2,
        name: 'DeepMind',
        logo: 'https://img.logo.dev/deepmind.google?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 3,
        name: 'Google Cloud',
        logo: 'https://img.logo.dev/googlecloudpresscorner.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 4,
        name: 'Microsoft',
        logo: 'https://img.logo.dev/microsoft.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 5,
        name: 'Apple',
        logo: 'https://img.logo.dev/apple.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 6,
        name: 'Amazon',
        logo: 'https://img.logo.dev/amazon.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 7,
        name: 'Meta',
        logo: 'https://img.logo.dev/meta.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 8,
        name: 'Netflix',
        logo: 'https://img.logo.dev/netflix.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 9,
        name: 'Tesla',
        logo: 'https://img.logo.dev/tesla.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 10,
        name: 'Spotify',
        logo: 'https://img.logo.dev/spotify.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    }
];

// Keyframes for continuous sliding animation
const slideAnimation = keyframes`
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
`;

const CompanyLogosSection = () => {
    // Create duplicated array for seamless infinite loop
    const duplicatedLogos = [...companyLogos, ...companyLogos];

    return (
        <Box sx={{ py: { xs: 3, md: 4 }, backgroundColor: '#ECEDF2' }}>
            <Container maxWidth="lg">
                {/* Sliding Company Logos Container */}
                <Box
                    sx={{
                        overflow: 'hidden',
                        position: 'relative',
                        width: '100%',
                        height: { xs: 80, md: 100 }
                    }}
                >
                    {/* Sliding Track */}
                    <Box
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            animation: `${slideAnimation} 50s linear infinite`,
                            width: '200%', // Double width to accommodate duplicated logos
                            height: '100%'
                        }}
                    >
                        {duplicatedLogos.map((company, index) => (
                            <Box
                                key={`${company.id}-${index}`}
                                sx={{
                                    flex: '0 0 auto',
                                    width: { xs: 'calc(100% / 5)', md: 'calc(100% / 6)' }, // Show 5 logos on mobile, 6 on desktop
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    px: { xs: 1.5, md: 2 }
                                }}
                            >
                                <Box
                                    component="img"
                                    src={company.logo}
                                    alt={company.name}
                                    sx={{
                                        maxWidth: '100%',
                                        height: 'auto',
                                        maxHeight: { xs: 60, md: 80 },
                                        opacity: 0.8,
                                        transition: 'all 0.3s ease',
                                        '&:hover': {
                                            opacity: 1,
                                            transform: 'scale(1.05)'
                                        }
                                    }}
                                />
                            </Box>
                        ))}
                    </Box>

                    {/* Gradient Overlays for smooth edges */}
                    <Box
                        sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: { xs: 40, md: 60 },
                            height: '100%',
                            background: 'linear-gradient(to right, #ECEDF2, transparent)',
                            pointerEvents: 'none',
                            zIndex: 1
                        }}
                    />
                    <Box
                        sx={{
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            width: { xs: 40, md: 60 },
                            height: '100%',
                            background: 'linear-gradient(to left, #ECEDF2, transparent)',
                            pointerEvents: 'none',
                            zIndex: 1
                        }}
                    />
                </Box>
            </Container>
        </Box>
    );
};

export default CompanyLogosSection;
