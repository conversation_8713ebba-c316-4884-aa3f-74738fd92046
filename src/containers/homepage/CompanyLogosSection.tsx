// material-ui
import { Box, Container, keyframes } from '@mui/material';

const companyLogos = [
    {
        id: 1,
        name: 'Google',
        logo: 'https://img.logo.dev/google.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 2,
        name: 'DeepMind',
        logo: 'https://img.logo.dev/deepmind.google?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 3,
        name: 'Google Cloud',
        logo: 'https://img.logo.dev/googlecloudpresscorner.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 4,
        name: 'Microsoft',
        logo: 'https://img.logo.dev/microsoft.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 5,
        name: 'Apple',
        logo: 'https://img.logo.dev/apple.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 6,
        name: 'Amazon',
        logo: 'https://img.logo.dev/amazon.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 7,
        name: 'Meta',
        logo: 'https://img.logo.dev/meta.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 8,
        name: 'Netflix',
        logo: 'https://img.logo.dev/netflix.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 9,
        name: 'Tesla',
        logo: 'https://img.logo.dev/tesla.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    },
    {
        id: 10,
        name: 'Spotify',
        logo: 'https://img.logo.dev/spotify.com?token=pk_JNtYBCy6RyOkevRWc-7ghw'
    }
];

// Keyframes for continuous sliding animation
const slideAnimation = keyframes`
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
`;

const CompanyLogosSection = () => {
    // Create duplicated array for seamless infinite loop
    const duplicatedLogos = [...companyLogos, ...companyLogos];

    return (
        <Box sx={{ py: { xs: 6, md: 10 }, backgroundColor: '#ECEDF2' }}>
            <Container maxWidth="lg">
                {/* Section Header */}
                <Box textAlign="center" mb={6}>
                    <Box
                        sx={{
                            width: '100%',
                            height: 1,
                            backgroundColor: '#D1D5DB',
                            mb: { xs: 4, md: 6 }
                        }}
                    />
                </Box>

                {/* Sliding Company Logos Container */}
                <Box
                    sx={{
                        overflow: 'hidden',
                        position: 'relative',
                        width: '100%',
                        height: { xs: 60, md: 80 }
                    }}
                >
                    {/* Sliding Track */}
                    <Box
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            animation: `${slideAnimation} 30s linear infinite`,
                            width: '200%', // Double width to accommodate duplicated logos
                            height: '100%'
                        }}
                    >
                        {duplicatedLogos.map((company, index) => (
                            <Box
                                key={`${company.id}-${index}`}
                                sx={{
                                    flex: '0 0 auto',
                                    width: { xs: 'calc(100% / 6)', md: 'calc(100% / 6)' }, // Show 6 logos at a time
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    px: { xs: 1, md: 2 }
                                }}
                            >
                                <Box
                                    component="img"
                                    src={company.logo}
                                    alt={company.name}
                                    sx={{
                                        maxWidth: '100%',
                                        height: 'auto',
                                        maxHeight: { xs: 40, md: 50 },
                                        opacity: 0.7,
                                        transition: 'opacity 0.3s ease',
                                        filter: 'grayscale(100%)',
                                        '&:hover': {
                                            opacity: 1,
                                            filter: 'grayscale(0%)'
                                        }
                                    }}
                                />
                            </Box>
                        ))}
                    </Box>

                    {/* Gradient Overlays for smooth edges */}
                    <Box
                        sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: 60,
                            height: '100%',
                            background: 'linear-gradient(to right, #ECEDF2, transparent)',
                            pointerEvents: 'none',
                            zIndex: 1
                        }}
                    />
                    <Box
                        sx={{
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            width: 60,
                            height: '100%',
                            background: 'linear-gradient(to left, #ECEDF2, transparent)',
                            pointerEvents: 'none',
                            zIndex: 1
                        }}
                    />
                </Box>
            </Container>
        </Box>
    );
};

export default CompanyLogosSection;
